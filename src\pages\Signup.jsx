import React from 'react'

import { FcGoogle } from "react-icons/fc";
import { SiFacebook } from "react-icons/si";
import { TfiApple } from "react-icons/tfi";

const Signup = () => {
    return (
        <>
            <div className="bg-bground overflow-x-hidden">
                <h1 className="text-2xl text-center font-semibold mt-5 p-5">Sign In</h1>
                <div className="flex flex-col p-5 relative">
                    <label htmlFor="phone" className='text-lg font-semibold px-2'>Phone Number</label>
                    <input type="text" name="phone" id="phone" className="border border-gray-400 p-3 rounded-xl text-center font-semibold text-lg mb-4" placeholder='Enter phone number'/>
                    <label htmlFor="password" className='text-lg font-semibold px-2 py-5'>Password</label>
                    <input type="text" name="password" id="password" className="border border-gray-400 p-3 rounded-xl text-center font-semibold text-lg mb-4" placeholder='Enter password'/>
                    <h2 className='text-lg text-primary font-semibold mt-2'><a href="">Forgot Password?</a></h2>
                    <div className="flex justify-center">
                        <button className='bg-accent text-gray-700 p-3 rounded-[40px] m-10 text-lg font-semibold w-full'>Sign In</button>
                    </div>
                    <div className="flex justify-between items-center">
                        <div className="w-1/2 h-0.5 bg-gray-300"></div>
                        <h2 className="px-5 text-lg font-bold text-gray-500">or</h2>
                        <div className="w-1/2 h-0.5 bg-gray-300"></div>
                    </div>


                    <div className="mt-5 flex flex-col items-center justify-center">
                        <button className="flex justify-center items-center gap-3 bg-blue-500 p-2 rounded-[40px] mb-4 w-full"><FcGoogle size={25}/><a href="" className="text-white text-lg font-semibold">Continue with Google</a></button>
                        <button className="flex justify-center items-center gap-3 bg-blue-900 p-2 rounded-[40px] mb-4 w-full"><SiFacebook size={25}/><a href="" className="text-white text-lg font-semibold">Continue with Facebook</a></button>
                        <button className="flex justify-center items-center gap-3 bg-black p-2 rounded-[40px] mb-4 w-full"><TfiApple size={25} className='text-white'/><a href="" className="text-white text-lg font-semibold">Continue with Apple</a></button>
                        <h2 className="text-lg font-bold text-gray-500 mt-5">Don't have an account? <a href="" className="text-primary">Sign Up</a></h2>
                        
                    </div>

                </div>
            </div>
        </>
    )
}

export default Signup